-- 创建学生错题状态表
-- 用于高效管理错题状态，避免复杂的SQL查询

CREATE TABLE student_wrong_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    
    -- 关联字段
    student_id BIGINT NOT NULL COMMENT '学生ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    knowledge_point_id BIGINT NOT NULL COMMENT '知识点ID',
    
    -- 错题状态
    status ENUM('WRONG', 'MASTERED', 'ELIMINATED') NOT NULL DEFAULT 'WRONG' COMMENT '错题状态：WRONG-错题，MASTERED-已掌握，ELIMINATED-已消除',
    
    -- 统计信息
    wrong_count INT NOT NULL DEFAULT 1 COMMENT '错误次数',
    first_wrong_time TIMESTAMP NOT NULL COMMENT '第一次答错时间',
    last_wrong_time TIMESTAMP NOT NULL COMMENT '最后一次答错时间',
    mastered_time TIMESTAMP NULL COMMENT '掌握时间（答对或手动标记）',
    
    -- 关联最新答题记录
    latest_answer_id BIGINT NULL COMMENT '最新答题记录ID',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 唯一约束：每个学生的每道题目只能有一条记录
    UNIQUE KEY uk_student_question (student_id, question_id),
    
    -- 索引优化
    INDEX idx_student_status (student_id, status) COMMENT '按学生和状态查询的主要索引',
    INDEX idx_student_wrong_time (student_id, last_wrong_time) COMMENT '按学生和错误时间排序的索引',
    INDEX idx_question_status (question_id, status) COMMENT '按题目和状态查询的索引',
    INDEX idx_knowledge_point (knowledge_point_id) COMMENT '按知识点查询的索引',
    INDEX idx_mastered_time (mastered_time) COMMENT '按掌握时间查询的索引',
    
    -- 外键约束
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (knowledge_point_id) REFERENCES knowledge_points(id) ON DELETE CASCADE,
    FOREIGN KEY (latest_answer_id) REFERENCES student_answers(id) ON DELETE SET NULL
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生错题状态表';

-- 创建用于错题消消乐的专用视图（可选，提升查询性能）
CREATE VIEW v_active_wrong_questions AS
SELECT 
    swq.*,
    s.username as student_username,
    s.real_name as student_name,
    q.question_type,
    kp.name as knowledge_point_name
FROM student_wrong_questions swq
JOIN students s ON swq.student_id = s.id
JOIN questions q ON swq.question_id = q.id
JOIN knowledge_points kp ON swq.knowledge_point_id = kp.id
WHERE swq.status = 'WRONG'
ORDER BY swq.last_wrong_time DESC;
