-- 创建学生章节权限表
-- 用于管理学生对特定章节的访问权限

CREATE TABLE student_chapter_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    
    -- 关联字段
    student_id BIGINT NOT NULL COMMENT '学生ID',
    chapter_id BIGINT NOT NULL COMMENT '章节ID',
    course_content_id BIGINT NOT NULL COMMENT '课程内容ID',
    
    -- 权限状态
    permission_status ENUM('GRANTED', 'DENIED', 'DEFAULT') NOT NULL DEFAULT 'DEFAULT' COMMENT '权限状态：GRANTED-已授权，DENIED-已拒绝，DEFAULT-默认（继承课程权限）',
    
    -- 权限生效时间
    granted_at TIMESTAMP NULL COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '权限过期时间（可选）',
    
    -- 操作记录
    granted_by BIGINT NULL COMMENT '授权人ID（经销商/代理商）',
    grant_reason VARCHAR(500) NULL COMMENT '授权原因',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 唯一约束：每个学生的每个章节只能有一条权限记录
    UNIQUE KEY uk_student_chapter (student_id, chapter_id),
    
    -- 外键约束
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    FOREIGN KEY (course_content_id) REFERENCES student_course_contents(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 索引优化
    INDEX idx_student_permissions (student_id, permission_status) COMMENT '按学生和权限状态查询的主要索引',
    INDEX idx_chapter_permissions (chapter_id, permission_status) COMMENT '按章节和权限状态查询的索引',
    INDEX idx_course_content (course_content_id) COMMENT '按课程内容查询的索引',
    INDEX idx_granted_by (granted_by) COMMENT '按授权人查询的索引',
    INDEX idx_expires_at (expires_at) COMMENT '按过期时间查询的索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生章节权限表';

-- 插入说明注释
INSERT INTO migration_notes (version, description, notes) VALUES 
('V20250717_001', '创建学生章节权限表', 
'新增student_chapter_permissions表用于管理学生章节访问权限。
支持三种权限状态：
- DEFAULT: 默认状态，继承课程权限（学生拥有课程下所有章节）
- GRANTED: 明确授权，学生可以访问该章节
- DENIED: 明确拒绝，学生不能访问该章节（用于自定义章节功能）

权限逻辑：
1. 如果没有记录或状态为DEFAULT，学生拥有所有章节权限
2. 如果状态为GRANTED，学生可以访问该章节
3. 如果状态为DENIED，学生不能访问该章节
4. 支持权限过期时间，过期后自动恢复为DEFAULT状态');
