-- 为学生表添加地区字段
-- 这个迁移脚本为students表添加region_id字段，用于支持地区筛选功能

-- 添加region_id字段
ALTER TABLE students 
ADD COLUMN region_id BIGINT;

-- 添加外键约束
ALTER TABLE students 
ADD CONSTRAINT fk_students_region 
FOREIGN KEY (region_id) REFERENCES regions(id);

-- 添加索引以提高查询性能
CREATE INDEX idx_students_region_id ON students(region_id);

-- 为现有学生数据设置默认地区（可选）
-- 这里可以根据实际需求设置默认地区，比如根据dealer的地区来设置
-- UPDATE students s 
-- SET region_id = (
--     SELECT urm.region_id 
--     FROM user_region_mappings urm 
--     WHERE urm.user_id = s.dealer_id 
--     AND urm.relation_type = 'MANAGEMENT' 
--     AND urm.is_primary = true 
--     LIMIT 1
-- ) 
-- WHERE s.region_id IS NULL AND s.dealer_id IS NOT NULL;
