-- 为student_course_contents表添加status字段以支持课程暂停功能
-- 作者: AI Assistant
-- 日期: 2025-07-17

-- 添加status字段
ALTER TABLE student_course_contents 
ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' 
COMMENT '课程状态: ACTIVE-正常, PAUSED-暂停, DISABLED-禁用';

-- 为status字段添加索引以提高查询性能
CREATE INDEX idx_student_course_contents_status ON student_course_contents(status);

-- 为现有数据设置默认状态
UPDATE student_course_contents 
SET status = 'ACTIVE' 
WHERE status IS NULL OR status = '';

-- 添加状态约束，确保只能使用预定义的状态值
ALTER TABLE student_course_contents 
ADD CONSTRAINT chk_student_course_contents_status 
CHECK (status IN ('ACTIVE', 'PAUSED', 'DISABLED'));
