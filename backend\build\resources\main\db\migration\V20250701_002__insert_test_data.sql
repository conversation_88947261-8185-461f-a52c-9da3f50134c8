-- 插入测试数据以支持练习结果保存功能测试

-- 确保有测试学生数据
INSERT IGNORE INTO students (id, username, password, real_name, phone, account_type, enabled, valid_from, valid_to, created_at, updated_at) 
VALUES (1, 'test_student', '$2a$10$N.zmdr9k7uOCQb96VdqHDeRUjKS/XdKJUKuNZNuaYXVtTkUdOW9Wy', '测试学生', '***********', 'TRIAL', 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());

-- 确保有测试科目数据
INSERT IGNORE INTO subjects (id, name, description, created_at, updated_at)
VALUES (1, '测试科目', '用于测试的科目', NOW(), NOW());

-- 确保有测试科目版本数据
INSERT IGNORE INTO subject_versions (id, subject_id, version_name, description, is_active, created_at, updated_at)
VALUES (1, 1, '测试版本', '用于测试的科目版本', 1, NOW(), NOW());

-- 确保有测试章节数据
INSERT IGNORE INTO chapters (id, subject_version_id, name, order_index, created_at, updated_at)
VALUES (1, 1, '测试章节', 1, NOW(), NOW());

-- 确保有测试知识点数据
INSERT IGNORE INTO knowledge_points (id, chapter_id, name, order_index, created_at, updated_at)
VALUES (1, 1, '测试知识点', 1, NOW(), NOW());

-- 确保有测试题目数据
INSERT IGNORE INTO questions (id, question_type, body, knowledge_point_id, created_at, updated_at)
VALUES 
(1, 'SINGLE_CHOICE', JSON_OBJECT(
    'type', 'SINGLE_CHOICE',
    'content', '<p>这是第一道测试题目</p>',
    'options', JSON_ARRAY('选项A', '选项B', '选项C', '选项D'),
    'answer', 'A',
    'explanation', '<p>正确答案是A</p>',
    'difficulty', 'EASY'
), 1, NOW(), NOW()),

(2, 'SINGLE_CHOICE', JSON_OBJECT(
    'type', 'SINGLE_CHOICE',
    'content', '<p>这是第二道测试题目</p>',
    'options', JSON_ARRAY('选项A', '选项B', '选项C', '选项D'),
    'answer', 'B',
    'explanation', '<p>正确答案是B</p>',
    'difficulty', 'MEDIUM'
), 1, NOW(), NOW()),

(3, 'SINGLE_CHOICE', JSON_OBJECT(
    'type', 'SINGLE_CHOICE',
    'content', '<p>这是第三道测试题目</p>',
    'options', JSON_ARRAY('选项A', '选项B', '选项C', '选项D'),
    'answer', 'C',
    'explanation', '<p>正确答案是C</p>',
    'difficulty', 'HARD'
), 1, NOW(), NOW()),

(4, 'FILL_IN_BLANK', JSON_OBJECT(
    'type', 'FILL_IN_BLANK',
    'content', '<p>这是一道填空题：2 + 2 = ___</p>',
    'answer', JSON_ARRAY('4'),
    'explanation', '<p>2 + 2 = 4</p>',
    'difficulty', 'EASY'
), 1, NOW(), NOW()),

(5, 'TRUE_FALSE', JSON_OBJECT(
    'type', 'TRUE_FALSE',
    'content', '<p>地球是圆的。</p>',
    'answer', true,
    'explanation', '<p>地球确实是圆的（准确地说是椭球形）</p>',
    'difficulty', 'EASY'
), 1, NOW(), NOW());
