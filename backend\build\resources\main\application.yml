spring:
  application:
    name: aistrusys-backend
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************************************
    username: rootay
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 数据库连接池优化
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      validation-timeout: 3000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
      pool-name: HikariPool-AIstrusys
      auto-commit: false
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
        # 性能优化配置
        jdbc:
          batch_size: 50
          fetch_size: 50
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        connection:
          provider_disables_autocommit: false
        cache:
          use_second_level_cache: false
          use_query_cache: false
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  main:
    allow-bean-definition-overriding: true
  # 异步处理配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: async-task-
    scheduling:
      pool:
        size: 5
      thread-name-prefix: scheduled-task-
  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s,expireAfterAccess=300s
    cache-names:
      - users
      - subjects
      - regions
      - accounts
      - stats
  # JMX配置
  jmx:
    enabled: true
  # Jackson配置优化
  jackson:
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null
    time-zone: GMT+8
  # 国际化配置
  messages:
    basename: messages
    encoding: UTF-8
    cache-duration: 3600
  # Web配置优化
  web:
    resources:
      cache:
        cachecontrol:
          max-age: 3600
          cache-public: true
      static-locations: classpath:/static/
  # MVC配置
  mvc:
    async:
      request-timeout: 30000
    servlet:
      load-on-startup: 1
    pathmatch:
      matching-strategy: ant_path_matcher

server:
  port: 8081
  # Tomcat性能优化
  tomcat:
    # 连接器配置
    threads:
      max: 200
      min-spare: 10
    # 连接池配置
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000
    keep-alive-timeout: 60000
    max-keep-alive-requests: 100
    # 压缩配置
    compression: on
    compressible-mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml
    min-response-size: 1024
    # 缓冲区配置
    max-http-form-post-size: 2MB
    max-swallow-size: 2MB
  # HTTP/2支持
  http2:
    enabled: true
  # 错误处理
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
    include-exception: false

jwt:
  secret: aistrusysSecretKeyForJWTAuthenticationVeryLongAndSecureKey2024
  expiration: 86400000

# 腾讯云短信服务配置
tencent:
  sms:
    secret-id: ${TENCENT_SECRET_ID:AKIDDIBrKchcKsLpDONCpV0HLBoDRRDl5C5l}
    secret-key: ${TENCENT_SECRET_KEY:WpxsZ37CXZfeQ1PZWRF0hMr8B0tdBWjm}
    region: ${TENCENT_SMS_REGION:ap-beijing}
    app-id: ${TENCENT_SMS_APP_ID:1401002978}
    # 注册验证码模板ID（用于登录验证码）- ID: 2464477
    # 内容：{1}为您的登录验证码，请于{2}分钟内填写，如非本人操作，请忽略本短信。
    register-template-id: ${TENCENT_SMS_REGISTER_TEMPLATE_ID:2464477}
    # 密码重置模板ID - ID: 2464478
    # 内容：您的动态验证码为：{1}，您正在进行密码重置操作，如非本人操作，请忽略本短信！
    password-reset-template-id: ${TENCENT_SMS_PASSWORD_RESET_TEMPLATE_ID:2464478}
    # 手机号修改模板ID - ID: 2464480
    # 内容：您正在修改注册手机号码，验证码为：{1}，5分钟有效，为保障帐户安全，请勿向任何人提供此验证码。
    phone-change-template-id: ${TENCENT_SMS_PHONE_CHANGE_TEMPLATE_ID:2464480}
    sign-name: ${TENCENT_SMS_SIGN_NAME:邻水县尚书坊培训}
    enabled: ${TENCENT_SMS_ENABLED:true}



# 日志配置优化
logging:
  level:
    root: INFO
    org.springframework.web: INFO
    org.hibernate: WARN
    org.hibernate.SQL: OFF
    org.hibernate.type.descriptor.sql: OFF
    com.example.backend: INFO
    # 网络相关日志
    org.apache.tomcat: INFO
    org.springframework.web.servlet.DispatcherServlet: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/aistrusys-backend.log
    max-size: 100MB
    max-history: 30

