import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Typography, Button, Empty, Spin, message } from 'antd';
import { ArrowLeftOutlined, LikeOutlined, DislikeOutlined, CheckCircleOutlined, ClockCircleOutlined, InboxOutlined, HistoryOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { getLearningTrackRecords } from '../api/user';
import { LearningAPI } from '../api/learning';
import IntelligentReportModal from '../components/ChapterQuizModule/scenes/IntelligentReportModal';
import QuestionDetailModal from '../components/common/QuestionDetailModal';
import HtmlContentRenderer from '../components/content/HtmlContentRenderer';
import { IntelligentReportDataTransformer } from '../api/intelligentReport';
import { BackButton } from '../components/common';
import '../styles/LearningTrack.css';

const { Header } = Layout;
const { Title } = Typography;

// 时间范围选项
const TIME_RANGES = [
  { key: 'today', label: '今日' },
  { key: 'week', label: '7天内' },
  { key: 'month', label: '30天内' },
  { key: '3months', label: '90天内' }
];

const LearningTrack = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState('week');
  const [learningRecords, setLearningRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // 章节测试相关状态
  const [chapterTestModalVisible, setChapterTestModalVisible] = useState(false);
  const [selectedChapterTestReport, setSelectedChapterTestReport] = useState(null);
  const [chapterTestLoading, setChapterTestLoading] = useState(false);

  // 题目详情相关状态
  const [questionModalVisible, setQuestionModalVisible] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [originalQuestions, setOriginalQuestions] = useState([]);
  const [originalUserAnswers, setOriginalUserAnswers] = useState({});

  // 处理章节测试点击
  const handleChapterTestClick = async (chapterTest) => {
    console.log('点击章节测试:', chapterTest);

    if (!chapterTest.sessionId) {
      console.error('章节测试缺少sessionId');
      message.error('无法打开报告：缺少会话信息');
      return;
    }

    setChapterTestLoading(true);
    setChapterTestModalVisible(true);

    try {
      // 调用API获取智能报告数据
      const { IntelligentReportAPI } = await import('../api/intelligentReport');
      const reportResponse = await IntelligentReportAPI.getReportBySessionId(chapterTest.sessionId);

      console.log('获取到的智能报告数据:', reportResponse);

      // 转换数据格式
      const transformedData = IntelligentReportDataTransformer.transformReportData(reportResponse.data);
      console.log('转换后的智能报告数据:', transformedData);

      // 获取完整的题目数据
      const practiceDetail = await LearningAPI.getPracticeDetail(chapterTest.sessionId);
      const convertedData = await LearningAPI.convertPracticeDetailToProps(practiceDetail);

      if (convertedData) {
        console.log('获取到完整题目数据:', convertedData);
        setOriginalQuestions(convertedData.questions || []);
        setOriginalUserAnswers(convertedData.userAnswers || {});
      }

      setSelectedChapterTestReport(transformedData);
    } catch (error) {
      console.error('获取智能报告失败:', error);
      message.error('获取智能报告失败，请稍后重试');
      setChapterTestModalVisible(false);
    } finally {
      setChapterTestLoading(false);
    }
  };

  // 处理题目点击
  const handleQuestionClick = (questionIndex) => {
    console.log('点击题目:', questionIndex);

    if (!originalQuestions || originalQuestions.length === 0) {
      console.error('没有题目数据');
      return;
    }

    const question = originalQuestions[questionIndex];
    const questionAnswer = originalUserAnswers[question?.id];

    if (question) {
      setSelectedQuestion({
        question,
        questionAnswer,
        index: questionIndex + 1
      });
      setQuestionModalVisible(true);
    }
  };

  // 关闭题目详情弹窗
  const handleQuestionModalClose = () => {
    setQuestionModalVisible(false);
    setSelectedQuestion(null);
  };

  // 智能渲染答案的函数
  const renderAnswer = useCallback((answer, errorPrefix = '答案') => {
    if (!answer) return <span>无答案</span>;

    // 如果答案是JSON字符串，尝试解析
    let parsedAnswer = answer;
    if (typeof answer === 'string') {
      try {
        parsedAnswer = JSON.parse(answer);
      } catch (e) {
        // 解析失败，保持原字符串
      }
    }

    // 处理数组形式的答案
    if (Array.isArray(parsedAnswer)) {
      return <HtmlContentRenderer htmlContent={parsedAnswer.join(', ')} />;
    }

    // 处理对象形式的答案
    if (typeof parsedAnswer === 'object' && parsedAnswer !== null) {
      const values = Object.values(parsedAnswer).filter(val => val && val !== '');
      return <HtmlContentRenderer htmlContent={values.join(', ')} />;
    }

    // 处理字符串形式的答案
    if (typeof parsedAnswer === 'string') {
      return <HtmlContentRenderer htmlContent={parsedAnswer} />;
    }

    // 其他情况直接转换为字符串
    return <HtmlContentRenderer htmlContent={String(parsedAnswer)} />;
  }, []);

  // 渲染填空题，将学生答案显示在下划线中
  const renderFillInBlankWithAnswers = useCallback((questionContent, answerData, correctAnswers) => {
    try {
      let content = questionContent;
      let blankIndex = 0;

      // 将下划线替换为学生答案
      content = content.replace(/_+/g, () => {
        const blankId = `blank_${blankIndex}`;
        const userAnswer = answerData[blankId] || '';

        // 处理正确答案 - 可能是数组格式
        let correctAnswer = '';
        if (Array.isArray(correctAnswers)) {
          correctAnswer = correctAnswers[blankIndex] || '';
        } else if (correctAnswers[blankId]) {
          correctAnswer = correctAnswers[blankId];
        }

        // 简单比较答案是否正确（忽略大小写和前后空格）
        const isCorrect = userAnswer.trim().toLowerCase() === correctAnswer.trim().toLowerCase();
        const statusIcon = isCorrect ? '✓' : '✗';
        const statusColor = isCorrect ? '#52c41a' : '#ff4d4f';

        blankIndex++;

        // 如果有答案，显示答案和状态图标
        if (userAnswer) {
          return `<span style="display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid ${statusColor}; background: transparent; text-align: center; position: relative;">
            ${userAnswer} <span style="color: ${statusColor}; font-weight: bold; margin-left: 4px;">${statusIcon}</span>
          </span>`;
        } else {
          // 没有答案，显示空白和错误图标
          return `<span style="display: inline-block; min-width: 60px; padding: 2px 8px; border-bottom: 2px solid #ff4d4f; background: transparent; text-align: center; position: relative;">
            <span style="color: #999;">(空)</span> <span style="color: #ff4d4f; font-weight: bold; margin-left: 4px;">✗</span>
          </span>`;
        }
      });

      return <HtmlContentRenderer htmlContent={content} />;
    } catch (error) {
      console.error('渲染填空题答案失败:', error);
      return '渲染失败';
    }
  }, []);

  // 渲染学生答案 - 复用ChapterResultScreen的逻辑
  const renderStudentAnswer = useCallback((questionAnswer, question) => {
    if (!questionAnswer || questionAnswer.isSkipped) {
      return <span style={{ color: '#999' }}>未作答</span>;
    }

    try {
      // 排除系统字段，获取用户的原始答案
      const { isCorrect, isSkipped, feedback, ...answerData } = questionAnswer;

      if (Object.keys(answerData).length === 0) {
        return <span style={{ color: '#999' }}>无答案数据</span>;
      }

      const questionType = question.questionDetails?.type || question.questionType;
      console.log('LearningTrack renderStudentAnswer - questionType:', questionType);
      console.log('LearningTrack renderStudentAnswer - question:', question);
      console.log('LearningTrack renderStudentAnswer - answerData:', answerData);
      console.log('LearningTrack renderStudentAnswer - answerData keys:', Object.keys(answerData));
      console.log('LearningTrack renderStudentAnswer - answerData values:', Object.values(answerData));

      switch (questionType) {
        case 'SINGLE_CHOICE':
        case 'MULTIPLE_CHOICE':
          // 如果有选项信息，显示详细的选项状态
          if (question.questionDetails?.options && Array.isArray(question.questionDetails.options)) {
            console.log('LearningTrack - 进入选项渲染逻辑');
            const options = question.questionDetails.options;
            // 处理用户选择的答案 - 可能在selectedOptions、answer字段或数字键中
            let userSelections = [];
            if (answerData.selectedOptions && Array.isArray(answerData.selectedOptions)) {
              userSelections = answerData.selectedOptions;
            } else if (answerData.answer) {
              // 如果answer是字符串（如"A"），转换为数组
              userSelections = Array.isArray(answerData.answer) ? answerData.answer : [answerData.answer];
            } else {
              // 检查数字键（如 {0: 'B', 1: 'C'}）
              const numericKeys = Object.keys(answerData).filter(key => !isNaN(key));
              if (numericKeys.length > 0) {
                userSelections = numericKeys.map(key => answerData[key]).filter(val => val);
              }
            }

            console.log('LearningTrack - userSelections:', userSelections);
            const correctAnswers = Array.isArray(question.questionDetails.answer) ?
              question.questionDetails.answer : [question.questionDetails.answer];
            console.log('LearningTrack - correctAnswers:', correctAnswers);

            return (
              <div style={{ marginTop: '8px' }}>
                {options.map((option, optionIndex) => {
                  const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, D...
                  const isSelected = userSelections.includes(optionLetter);
                  const isCorrect = correctAnswers.includes(optionLetter);

                  let backgroundColor = 'transparent';
                  let borderColor = '#d9d9d9';
                  let textColor = '#000';
                  let icon = '';
                  let label = '';

                  if (isSelected && isCorrect) {
                    // 用户选择且正确
                    backgroundColor = '#f6ffed';
                    borderColor = '#52c41a';
                    textColor = '#52c41a';
                    icon = '✓';
                    label = '(您的选择 - 正确)';
                  } else if (isSelected && !isCorrect) {
                    // 用户选择但错误
                    backgroundColor = '#fff2f0';
                    borderColor = '#ff4d4f';
                    textColor = '#ff4d4f';
                    icon = '✗';
                    label = '(您的选择 - 错误)';
                  } else if (!isSelected && isCorrect) {
                    // 正确答案但用户未选择
                    backgroundColor = '#f6ffed';
                    borderColor = '#52c41a';
                    textColor = '#52c41a';
                    icon = '✓';
                    label = '(正确答案)';
                  }

                  return (
                    <div
                      key={optionIndex}
                      style={{
                        marginBottom: '4px',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        backgroundColor,
                        border: `2px solid ${borderColor}`,
                        fontWeight: (isSelected || isCorrect) ? 'bold' : 'normal',
                        color: textColor
                      }}
                    >
                      {icon && <span style={{ marginRight: '8px' }}>{icon}</span>}
                      {optionLetter}. <HtmlContentRenderer htmlContent={option} />
                      {label && <span style={{ marginLeft: '8px' }}>{label}</span>}
                    </div>
                  );
                })}
              </div>
            );
          }

          // 如果没有选项，回退到原来的显示方式
          if (answerData.selectedOptions) {
            const answerText = answerData.selectedOptions.join(', ');
            return <HtmlContentRenderer htmlContent={answerText} />;
          } else if (Object.keys(answerData).length > 0) {
            const values = Object.values(answerData).filter(v => v !== null && v !== undefined);
            if (values.length > 0) {
              const answerText = values.join(', ');
              return <HtmlContentRenderer htmlContent={answerText} />;
            }
            return '未选择';
          }
          return '未选择';

        case 'TRUE_FALSE':
          return answerData.answer !== undefined ?
            (answerData.answer ? '正确' : '错误') : '未选择';

        case 'FILL_IN_BLANK':
          // 对于填空题，将学生答案显示在题目的下划线中
          const questionContent = question.questionDetails?.content || question.content;
          if (questionContent && answerData && typeof answerData === 'object') {
            // 获取正确答案用于比较
            let correctAnswers = {};
            try {
              if (question.questionDetails?.answer) {
                if (typeof question.questionDetails.answer === 'string') {
                  correctAnswers = JSON.parse(question.questionDetails.answer);
                } else {
                  correctAnswers = question.questionDetails.answer;
                }
              }
            } catch (error) {
              console.error('解析正确答案失败:', error);
            }

            return renderFillInBlankWithAnswers(questionContent, answerData, correctAnswers);
          }
          return '未填写';

        default:
          return JSON.stringify(answerData);
      }
    } catch (error) {
      console.error('渲染学生答案失败:', error);
      return '答案解析失败';
    }
  }, [renderFillInBlankWithAnswers]);

  // 渲染正确答案
  const renderCorrectAnswer = useCallback((question) => {
    try {
      const questionDetails = question.questionDetails;
      console.log('renderCorrectAnswer - question:', question);
      console.log('renderCorrectAnswer - questionDetails:', questionDetails);

      // 复合题目的处理逻辑
      if (questionDetails?.subQuestions && Array.isArray(questionDetails.subQuestions)) {
        console.log('处理复合题目的正确答案');
        return (
          <div>
            {questionDetails.subQuestions.map((subQuestion, index) => (
              <div key={index} style={{ marginBottom: '8px' }}>
                <strong>子题{index + 1}：</strong>
                {renderAnswer(subQuestion.answer, `子题${index + 1}答案`)}
              </div>
            ))}
          </div>
        );
      }

      // 普通题目的处理逻辑
      let answer = questionDetails?.answer || question.answer;
      console.log('普通题目答案:', answer);
      if (!answer || answer === '') {
        console.log('答案为空，返回默认提示');
        return <span>答案未找到</span>;
      }

      return renderAnswer(answer, '正确答案');
    } catch (error) {
      console.error('渲染正确答案失败:', error);
      return <span>答案解析失败</span>;
    }
  }, [renderAnswer]);

  // 渲染正确答案（支持HTML）
  const renderCorrectAnswerWithHtml = useCallback((question) => {
    try {
      const questionDetails = question.questionDetails;
      const questionType = questionDetails?.type || question.questionType;
      const answer = questionDetails?.answer;

      switch (questionType) {
        case 'SINGLE_CHOICE':
        case 'MULTIPLE_CHOICE':
          // 获取题目选项
          const options = questionDetails?.options || question.options;

          if (options && Array.isArray(options) && answer) {
            // 找到正确答案对应的选项内容
            const answerIndex = answer.charCodeAt(0) - 65; // A=0, B=1, C=2, D=3
            if (answerIndex >= 0 && answerIndex < options.length) {
              const correctOptionContent = options[answerIndex];
              return (
                <div>
                  <strong>{answer}. </strong>
                  <HtmlContentRenderer htmlContent={correctOptionContent} />
                </div>
              );
            }
          }

          // 降级处理
          if (Array.isArray(answer)) {
            const answerText = answer.join(', ');
            if (answerText.includes('<')) {
              return <HtmlContentRenderer htmlContent={answerText} />;
            }
            return answerText;
          }
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer || '答案未设置';

        case 'TRUE_FALSE':
          return answer === true ? '正确' : answer === false ? '错误' : '答案未设置';

        case 'FILL_IN_BLANK':
          if (Array.isArray(answer)) {
            const answerText = answer.join(', ');
            // 如果答案包含HTML标签，使用HtmlContentRenderer
            if (answerText.includes('<')) {
              return <HtmlContentRenderer htmlContent={answerText} />;
            }
            return answerText;
          }
          // 如果答案包含HTML标签，使用HtmlContentRenderer
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer || '答案未设置';

        default:
          if (answer && typeof answer === 'string' && answer.includes('<')) {
            return <HtmlContentRenderer htmlContent={answer} />;
          }
          return answer ? String(answer) : '答案未设置';
      }
    } catch (error) {
      return '答案解析失败';
    }
  }, []);

  // 获取学习记录
  useEffect(() => {
    const fetchLearningRecords = async () => {
      setLoading(true);
      try {
        // 调用API获取学习记录
        const response = await getLearningTrackRecords(selectedTimeRange);

        if (response && response.data && Array.isArray(response.data)) {
          // 如果API返回了有效数据，使用API数据
          console.log('学习轨迹API返回数据:', response.data);
          const processedData = processApiData(response.data);
          console.log('处理后的学习轨迹数据:', processedData);
          setLearningRecords(processedData);
        } else {
          // 没有数据时设置为空数组
          console.log('API未返回有效数据，设置为空');
          setLearningRecords([]);
        }
      } catch (error) {
        console.error('获取学习记录失败:', error);
        message.error('获取学习记录失败，请稍后重试');

        // 发生错误时也设置为空数组，不显示任何内容
        setLearningRecords([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLearningRecords();
  }, [selectedTimeRange]);
  
  // 处理API返回的数据
  const processApiData = (apiData) => {
    console.log('开始处理API数据:', apiData);
    console.log('API数据类型:', typeof apiData, '是否为数组:', Array.isArray(apiData));

    if (!Array.isArray(apiData)) {
      console.error('API数据不是数组格式:', apiData);
      return [];
    }

    // 收集所有学习活动记录
    const allActivities = [];

    // 处理每条记录，展开为独立的学习活动
    apiData.forEach((record, recordIndex) => {
      console.log(`处理第${recordIndex}条记录:`, record);
      console.log('记录类型:', typeof record, '记录键:', Object.keys(record));

      // 处理基础日期
      let baseDate;
      if (record.date) {
        console.log('原始日期值:', record.date, '类型:', typeof record.date);
        baseDate = new Date(record.date);
        console.log('转换后的日期:', baseDate);
      } else if (record.startTime) {
        baseDate = new Date(record.startTime);
      } else {
        baseDate = new Date();
      }

      // 检查日期是否有效
      if (isNaN(baseDate.getTime())) {
        console.error('Invalid date value:', record.date || record.startTime);
        baseDate = new Date(); // 使用当前日期作为fallback
      }

      // 处理知识点学习活动
      if (record.knowledgePoints && Array.isArray(record.knowledgePoints)) {
        console.log('知识点数组长度:', record.knowledgePoints.length);
        record.knowledgePoints.forEach((kp, kpIndex) => {
          console.log(`处理第${kpIndex}个知识点:`, kp);

          // 为每个知识点创建独立的学习活动记录
          const activityDate = kp.practiceTime ? new Date(kp.practiceTime) : baseDate;

          allActivities.push({
            id: `kp_${recordIndex}_${kpIndex}_${activityDate.getTime()}`, // 确保唯一性
            type: 'KNOWLEDGE_POINT',
            date: activityDate,
            timestamp: activityDate.getTime(),
            name: kp.name || '知识点练习',
            sessionId: kp.sessionId || null,
            knowledgePointId: kp.id,
            practiceCount: kp.practiceCount || 1,
            mastered: kp.mastered || false,
            score: kp.score || null,
            accuracyRate: kp.accuracyRate || null,
            duration: kp.duration || null,
            summary: `练习了知识点：${kp.name || '未知知识点'}`
          });
        });
      }

      // 处理章节测试活动
      if (record.chapterTests && Array.isArray(record.chapterTests)) {
        console.log('章节测试数组长度:', record.chapterTests.length);
        record.chapterTests.forEach((test, testIndex) => {
          console.log(`处理第${testIndex}个章节测试:`, test);

          // 为每个章节测试创建独立的学习活动记录
          const activityDate = test.generatedAt ? new Date(test.generatedAt) :
                              test.completedAt ? new Date(test.completedAt) : baseDate;

          allActivities.push({
            id: `ct_${recordIndex}_${testIndex}_${activityDate.getTime()}`, // 确保唯一性
            type: 'CHAPTER_TEST',
            date: activityDate,
            timestamp: activityDate.getTime(),
            name: test.chapterName || '章节测试',
            sessionId: test.sessionId || null,
            chapterId: test.chapterId || null,
            score: test.score || 0,
            maxScore: test.maxScore || 100,
            accuracyRate: test.accuracyRate || 0,
            duration: test.duration || null,
            summary: `完成了章节测试：${test.chapterName || '未知章节'}`
          });
        });
      }
    });

    console.log('所有学习活动收集完成，数量:', allActivities.length);

    // 按时间戳降序排序（最新的在前面）
    const sortedActivities = allActivities.sort((a, b) => {
      const result = b.timestamp - a.timestamp;
      console.log('排序比较:', new Date(a.timestamp), 'vs', new Date(b.timestamp), '结果:', result);
      return result;
    });

    // 按日期分组，但保持每个活动的独立性
    const groupedByDate = {};
    sortedActivities.forEach(activity => {
      const dateKey = activity.date.toDateString();
      if (!groupedByDate[dateKey]) {
        groupedByDate[dateKey] = {
          date: activity.date,
          activities: []
        };
      }
      groupedByDate[dateKey].activities.push(activity);
    });

    // 转换为数组格式，保持日期降序
    const finalResult = Object.values(groupedByDate).sort((a, b) => b.date - a.date);

    console.log('最终处理结果:', finalResult);
    console.log('最终结果数量:', finalResult.length);

    return finalResult;
  };







  // 格式化日期
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  };

  // 处理时间范围选择
  const handleTimeRangeChange = (range) => {
    setSelectedTimeRange(range);
  };

  // 返回上一页
  const handleBack = () => {
    navigate('/study/home');
  };

  // 处理知识点点击 - 显示具体的学习结果
  const handleKnowledgePointClick = async (pointId, sessionId) => {
    console.log('点击知识点 - pointId:', pointId, 'sessionId:', sessionId);

    // 如果是模拟数据（ID以kp-开头），则不导航
    if (typeof pointId === 'string' && pointId.startsWith('kp-')) {
      message.info('这是模拟数据，无法导航到实际的知识点页面');
      return;
    }

    try {
      if (sessionId) {
        // 如果有sessionId，直接跳转到结果页面显示历史数据
        console.log('跳转到结果页面，pointId:', pointId, 'sessionId:', sessionId);
        navigate(`/study/result/${pointId}`, {
          state: {
            sessionId: sessionId,
            fromLearningTrack: true
          }
        });
      } else {
        // 如果没有sessionId，提示用户并跳转到学习页面
        console.log('没有sessionId，跳转到学习页面');
        message.info('该知识点暂无学习记录，将开始新的学习');
        navigate(`/study/knowledge-point/${pointId}`);
      }
    } catch (error) {
      console.error('跳转失败:', error);
      message.error('跳转失败，请稍后重试');
    }
  };

  // 获取星期几
  const getWeekDay = (dateString) => {
    const date = new Date(dateString);
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekDays[date.getDay()];
  };

  // 渲染时间筛选器
  const renderTimeFilter = () => {
    return (
      <div className="time-filter">
        {TIME_RANGES.map(option => (
          <div
            key={option.key}
            className={`time-filter-item ${selectedTimeRange === option.key ? 'active' : ''}`}
            onClick={() => handleTimeRangeChange(option.key)}
          >
            {option.label}
          </div>
        ))}
      </div>
    );
  };

  // 渲染学习轨迹时间线
  const renderTimeline = () => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" tip="加载中..." />
        </div>
      );
    }

    if (!learningRecords || learningRecords.length === 0) {
      return (
        <Empty
          className="empty-state"
          image={<InboxOutlined className="empty-icon" />}
          description="暂无学习记录"
        />
      );
    }

    return (
      <div className="timeline-container">
        {learningRecords.map((dateGroup, index) => (
          <div key={index} className="date-group">
            <div className="date-header">
              <div className="date-circle">
                {formatDate(dateGroup.date).split('年')[1].split('月')[1].replace('日', '')}
              </div>
              <div className="date-info">
                <div className="date-text">
                  {formatDate(dateGroup.date)} {getWeekDay(dateGroup.date)}
                </div>
                <div className="date-summary">
                  共 {dateGroup.activities.length} 项学习活动
                </div>
              </div>
            </div>

            {/* 学习活动列表 - 按时间倒序显示所有活动 */}
            <div className="knowledge-point-list">
              {dateGroup.activities.map((activity) => {
                if (activity.type === 'KNOWLEDGE_POINT') {
                  return (
                    <div
                      key={activity.id}
                      className={`knowledge-point-card ${activity.mastered ? 'mastered' : 'learning'}`}
                      onClick={() => handleKnowledgePointClick(activity.knowledgePointId, activity.sessionId)}
                    >
                      <div className="knowledge-point-info">
                        <div className="knowledge-point-name">{activity.name}</div>
                        <div className="knowledge-point-stats">
                          <span className="practice-count">
                            {activity.practiceCount > 1 ? `第 ${activity.practiceCount} 次练习` : '首次练习'}
                          </span>
                          <span className="practice-time">
                            {activity.date.toLocaleTimeString('zh-CN', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          <span className="mastery-status">
                            {activity.mastered ? (
                              <>
                                <CheckCircleOutlined className="mastery-icon" />
                                已掌握
                              </>
                            ) : (
                              <>
                                <ClockCircleOutlined className="mastery-icon" />
                                未掌握
                              </>
                            )}
                          </span>
                        </div>
                      </div>
                      {activity.mastered ?
                        <LikeOutlined className="status-icon" /> :
                        <DislikeOutlined className="status-icon" />
                      }
                    </div>
                  );
                } else if (activity.type === 'CHAPTER_TEST') {
                  return (
                    <div
                      key={activity.id}
                      className={`knowledge-point-card ${activity.accuracyRate >= 80 ? 'excellent' : activity.accuracyRate >= 60 ? 'good' : 'needs-improvement'}`}
                      onClick={() => handleChapterTestClick(activity)}
                    >
                      <div className="knowledge-point-info">
                        <div className="knowledge-point-name">{activity.name}</div>
                        <div className="knowledge-point-stats">
                          <span className="practice-count">{activity.score}/{activity.maxScore}分</span>
                          <span className="practice-time">
                            {activity.date.toLocaleTimeString('zh-CN', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          <span className="mastery-status">
                            <span className="mastery-icon">📊</span>
                            正确率 {activity.accuracyRate.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      {activity.accuracyRate >= 80 ? (
                        <LikeOutlined className="status-icon" />
                      ) : activity.accuracyRate >= 60 ? (
                        <ClockCircleOutlined className="status-icon" />
                      ) : (
                        <DislikeOutlined className="status-icon" />
                      )}
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Layout className="learning-track-layout">
      <Header className="learning-track-header">
        <div className="header-content">
          <BackButton onClick={handleBack} />
          <Title level={4} className="header-title">
            <HistoryOutlined />
            学习轨迹
          </Title>
        </div>
      </Header>

      <div className="content-area">
        <div className="content-header">
          {renderTimeFilter()}
        </div>
        {renderTimeline()}
      </div>

      {/* 智能学习报告弹窗 */}
      {selectedChapterTestReport && (
        <IntelligentReportModal
          visible={chapterTestModalVisible}
          onClose={() => {
            setChapterTestModalVisible(false);
            setSelectedChapterTestReport(null);
            setOriginalQuestions([]);
            setOriginalUserAnswers({});
          }}
          reportData={selectedChapterTestReport}
          userName="学生"
          chapterName={selectedChapterTestReport.chapterName || '章节测试'}
          loading={chapterTestLoading}
          fullscreen={false}
          onQuestionClick={handleQuestionClick}
          originalQuestions={originalQuestions}
          originalUserAnswers={originalUserAnswers}
        />
      )}

      {/* 题目详情弹窗 */}
      <QuestionDetailModal
        visible={questionModalVisible}
        onClose={handleQuestionModalClose}
        selectedQuestion={selectedQuestion}
        renderStudentAnswer={renderStudentAnswer}
        renderCorrectAnswer={renderCorrectAnswerWithHtml}
        renderFillInBlankWithAnswers={renderFillInBlankWithAnswers}
      />
    </Layout>
  );
};

export default LearningTrack; 