-- 删除旧的练习会话表（如果存在）
DROP TABLE IF EXISTS practice_sessions;
DROP TABLE IF EXISTS practice_question_answers;

-- 创建练习会话表
CREATE TABLE practice_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    session_type ENUM('<PERSON><PERSON><PERSON>LEDGE_POINT_PRACTICE', 'CHAPTER_TEST') NOT NULL,
    
    -- 基本统计
    total_questions INT NOT NULL,
    correct_questions INT NOT NULL,
    completion_rate DECIMAL(5,2) NOT NULL COMMENT '完成率 0-100',
    accuracy_rate DECIMAL(5,2) NOT NULL COMMENT '正确率 0-100',
    
    -- 题目对错详情
    question_results JSON NOT NULL COMMENT '每道题的对错情况',
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (knowledge_point_id) REFERENCES knowledge_points(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_student_knowledge_point (student_id, knowledge_point_id),
    INDEX idx_session_type (session_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习会话表';

-- 创建练习题目答题详情表
CREATE TABLE practice_question_answers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    practice_session_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    
    -- 题目信息
    question_order INT NOT NULL COMMENT '题目序号（1,2,3...）',
    question_snapshot JSON NOT NULL COMMENT '题目内容快照',
    
    -- 答题数据
    student_answer JSON COMMENT '学生答案',
    correct_answer JSON NOT NULL COMMENT '正确答案',
    answer_status ENUM('CORRECT', 'INCORRECT', 'SKIPPED', 'NOT_ANSWERED') NOT NULL,
    
    -- 外键约束
    FOREIGN KEY (practice_session_id) REFERENCES practice_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_session_order (practice_session_id, question_order),
    INDEX idx_session_question (practice_session_id, question_id),
    INDEX idx_answer_status (answer_status),
    
    -- 唯一约束：同一练习会话中，每道题只能有一条记录
    UNIQUE KEY uk_session_question (practice_session_id, question_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='练习题目答题详情表';
