-- 创建智能学习报告表
CREATE TABLE intelligent_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT NOT NULL,
    session_id BIGINT NOT NULL,
    session_type ENUM('KNOWLEDGE_POINT_PRACTICE', 'CHAPTER_TEST') NOT NULL,
    
    -- 基础统计数据
    total_score INT NOT NULL COMMENT '总得分',
    max_score INT NOT NULL COMMENT '满分',
    accuracy_rate DECIMAL(5,2) NOT NULL COMMENT '正确率 0-100',
    duration_seconds INT NOT NULL COMMENT '测试用时（秒）',
    
    -- 知识点统计
    total_knowledge_points INT NOT NULL COMMENT '总知识点数',
    mastered_knowledge_points INT NOT NULL COMMENT '已掌握知识点数',
    weak_knowledge_points INT NOT NULL COMMENT '薄弱知识点数',
    
    -- 详细数据（JSON格式）
    question_analysis JSON NOT NULL COMMENT '题目分析数据',
    knowledge_point_analysis JSON NOT NULL COMMENT '知识点分析数据',
    mastery_chart_data JSON NOT NULL COMMENT '掌握程度图表数据',
    
    -- 元数据
    chapter_id BIGINT COMMENT '章节ID',
    chapter_name VARCHAR(255) COMMENT '章节名称',
    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    
    -- 外键约束
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES practice_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_student_generated (student_id, generated_at),
    INDEX idx_session_type (session_type),
    INDEX idx_chapter (chapter_id),
    INDEX idx_session_unique (session_id),
    
    -- 唯一约束：每个练习会话只能有一个智能报告
    UNIQUE KEY uk_session_report (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='智能学习报告表';
